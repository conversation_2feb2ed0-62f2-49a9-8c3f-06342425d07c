"use client";

import { useState } from "react";
import { Prisma } from "@prisma/client";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@udoy/components/ui/table";
import { Button } from "@udoy/components/ui/button";
import { Badge } from "@udoy/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@udoy/components/ui/dropdown-menu";
import { MoreHorizontal, Edit, Trash2, MapPin } from "lucide-react";
import { EditZoneDialog } from "./edit-zone-dialog";
import { DeleteZoneDialog } from "./delete-zone-dialog";

type DeliveryZoneWithRelations = Prisma.DeliveryZoneGetPayload<{
  include: {
    subZones: {
      include: {
        subZones: true;
        parentZone: true;
        _count: {
          select: {
            address: true;
          };
        };
      };
    };
    parentZone: true;
    _count: {
      select: {
        address: true;
      };
    };
  };
}>;

interface DeliveryZonesListProps {
  zones: DeliveryZoneWithRelations[];
}

export function DeliveryZonesList({ zones }: DeliveryZonesListProps) {
  const [editingZone, setEditingZone] =
    useState<DeliveryZoneWithRelations | null>(null);
  const [deletingZone, setDeletingZone] =
    useState<DeliveryZoneWithRelations | null>(null);

  // Organize zones into parent-child hierarchy
  const parentZones = zones.filter((zone) => !zone.parentId);

  const renderZoneRow = (zone: DeliveryZoneWithRelations, level = 0) => {
    const indent = level * 20;

    return (
      <TableRow key={zone.id}>
        <TableCell>
          <div
            className="flex items-center"
            style={{ paddingLeft: `${indent}px` }}
          >
            {level > 0 && (
              <span className="mr-2 text-muted-foreground">└─</span>
            )}
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4" />
              <span className="font-medium">{zone.name}</span>
              {zone.nam && (
                <span className="text-sm text-muted-foreground">
                  ({zone.nam})
                </span>
              )}
            </div>
          </div>
        </TableCell>
        <TableCell>
          <code className="text-sm bg-muted px-2 py-1 rounded">
            {zone.slug}
          </code>
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            <span>৳{zone.charge}</span>
            {zone.express > 0 && (
              <Badge variant="secondary">Express: ৳{zone.express}</Badge>
            )}
          </div>
        </TableCell>
        <TableCell>
          <div className="flex items-center space-x-2">
            <Badge variant={zone.isBase ? "default" : "secondary"}>
              {zone.isBase ? "Base Zone" : "Sub Zone"}
            </Badge>
            {zone?.subZones?.length > 0 && (
              <Badge variant="outline">
                {zone?.subZones?.length} sub-zones
              </Badge>
            )}
          </div>
        </TableCell>
        <TableCell>
          <Badge variant="outline">{zone._count?.address || 0} addresses</Badge>
        </TableCell>
        <TableCell>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setEditingZone(zone)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => setDeletingZone(zone)}
                className="text-destructive"
                disabled={(zone._count?.address || 0) > 0 || (zone.subZones?.length || 0) > 0}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </TableCell>
      </TableRow>
    );
  };

  const renderZoneWithChildren = (
    zone: DeliveryZoneWithRelations,
    level = 0
  ) => {
    const rows = [renderZoneRow(zone, level)];

    // Add child zones - safely check if subZones exists and is an array
    if (zone.subZones && Array.isArray(zone.subZones)) {
      zone.subZones.forEach((subZone) => {
        rows.push(
          ...renderZoneWithChildren(
            subZone as DeliveryZoneWithRelations,
            level + 1
          )
        );
      });
    }

    return rows;
  };

  return (
    <>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Zone Name</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead>Charges</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Usage</TableHead>
              <TableHead className="w-[70px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {parentZones.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={6}
                  className="text-center py-8 text-muted-foreground"
                >
                  No delivery zones found. Create your first zone to get
                  started.
                </TableCell>
              </TableRow>
            ) : (
              parentZones.flatMap((zone) => renderZoneWithChildren(zone))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Edit Dialog */}
      {editingZone && (
        <EditZoneDialog
          zone={editingZone}
          zones={zones}
          isOpen={!!editingZone}
          onClose={() => setEditingZone(null)}
        />
      )}

      {/* Delete Dialog */}
      {deletingZone && (
        <DeleteZoneDialog
          zone={deletingZone}
          isOpen={!!deletingZone}
          onClose={() => setDeletingZone(null)}
        />
      )}
    </>
  );
}
