"use client";

import { useState } from "react";
import { But<PERSON> } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import { Badge } from "@udoy/components/ui/badge";
import { Edit, DollarSign, TrendingUp, TrendingDown } from "lucide-react";
import { updateCustomerBalance, type BalanceUpdateInput } from "../customers/actions";
import { toast } from "sonner";
import { isActionError } from "@udoy/utils/app-error";

interface CustomerBalanceInlineProps {
  customerId: number;
  customerName: string;
  currentBalance: number;
}

export function CustomerBalanceInline({
  customerId,
  customerName,
  currentBalance,
}: CustomerBalanceInlineProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [amount, setAmount] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount) {
      toast.error("Please enter an amount");
      return;
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount)) {
      toast.error("Please enter a valid amount");
      return;
    }

    setIsLoading(true);

    try {
      const result = await updateCustomerBalance({
        userId: customerId,
        amount: numericAmount,
        reason: "Balance updated", // Default reason since we removed the field
      });

      if (isActionError(result)) {
        toast.error(result.error.message);
      } else {
        toast.success("Customer balance updated successfully");
        setIsDialogOpen(false);
        setAmount("");
        // The page will be revalidated by the server action
        window.location.reload();
      }
    } catch (error) {
      toast.error("Failed to update balance");
    } finally {
      setIsLoading(false);
    }
  };

  const getBalanceDisplay = () => {
    if (currentBalance === 0) {
      return {
        text: "৳0",
        variant: "secondary" as const,
        icon: DollarSign,
        description: "Balanced",
      };
    } else if (currentBalance > 0) {
      return {
        text: `৳${currentBalance.toLocaleString()}`,
        variant: "default" as const,
        icon: TrendingUp,
        description: "Credit",
      };
    } else {
      return {
        text: `৳${Math.abs(currentBalance).toLocaleString()}`,
        variant: "destructive" as const,
        icon: TrendingDown,
        description: "Due",
      };
    }
  };

  const balanceInfo = getBalanceDisplay();
  const BalanceIcon = balanceInfo.icon;

  return (
    <div className="pt-4 border-t">
      <div className="flex items-center justify-between mb-3">
        <span className="text-sm font-medium">Balance:</span>
        <div className="flex items-center gap-2">
          <BalanceIcon className="h-4 w-4" />
          <span className="font-medium">{balanceInfo.text}</span>
          <Badge variant={balanceInfo.variant}>
            {balanceInfo.description}
          </Badge>
        </div>
      </div>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="w-full">
            <Edit className="mr-2 h-4 w-4" />
            Update Balance
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Update Customer Balance</DialogTitle>
            <DialogDescription>
              Set the new balance amount for {customerName}.
              Positive values indicate credit (overpayment),
              negative values indicate due amount.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="amount">New Balance Amount (৳)</Label>
                <Input
                  id="amount"
                  type="number"
                  step="0.01"
                  placeholder="Enter amount (e.g., -100 for due, 50 for credit)"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  required
                />
                <p className="text-xs text-muted-foreground">
                  Current balance: ৳{currentBalance}
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Updating..." : "Update Balance"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
