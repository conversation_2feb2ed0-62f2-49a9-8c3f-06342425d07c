"use client";

import { useState } from "react";
import { Button } from "@udoy/components/ui/button";
import { Input } from "@udoy/components/ui/input";
import { Label } from "@udoy/components/ui/label";
import { Textarea } from "@udoy/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@udoy/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@udoy/components/ui/card";
import { Badge } from "@udoy/components/ui/badge";
import { Edit, DollarSign, TrendingUp, TrendingDown } from "lucide-react";
import { updateCustomerBalance, type BalanceUpdateInput } from "../customers/actions";
import { toast } from "sonner";
import { isActionError } from "@udoy/utils/app-error";

interface CustomerBalanceManagementProps {
  customerId: number;
  customerName: string;
  currentBalance: number;
}

export function CustomerBalanceManagement({
  customerId,
  customerName,
  currentBalance,
}: CustomerBalanceManagementProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [amount, setAmount] = useState("");
  const [reason, setReason] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || !reason) {
      toast.error("Please fill in all fields");
      return;
    }

    const numericAmount = parseFloat(amount);
    if (isNaN(numericAmount)) {
      toast.error("Please enter a valid amount");
      return;
    }

    setIsLoading(true);

    try {
      const result = await updateCustomerBalance({
        userId: customerId,
        amount: numericAmount,
        reason: reason.trim(),
      });

      if (isActionError(result)) {
        toast.error(result.error.message);
      } else {
        toast.success("Customer balance updated successfully");
        setIsDialogOpen(false);
        setAmount("");
        setReason("");
        // The page will be revalidated by the server action
        window.location.reload();
      }
    } catch (error) {
      toast.error("Failed to update balance");
    } finally {
      setIsLoading(false);
    }
  };

  const getBalanceDisplay = () => {
    if (currentBalance === 0) {
      return {
        text: "৳0",
        variant: "secondary" as const,
        icon: DollarSign,
        description: "No outstanding balance",
      };
    } else if (currentBalance > 0) {
      return {
        text: `৳${currentBalance.toLocaleString()}`,
        variant: "default" as const,
        icon: TrendingUp,
        description: "Credit balance (customer has overpaid)",
      };
    } else {
      return {
        text: `৳${Math.abs(currentBalance).toLocaleString()}`,
        variant: "destructive" as const,
        icon: TrendingDown,
        description: "Due amount (customer owes money)",
      };
    }
  };

  const balanceInfo = getBalanceDisplay();
  const BalanceIcon = balanceInfo.icon;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle className="text-lg">Customer Balance</CardTitle>
          <CardDescription>
            Manage due/credit balance for {customerName}
          </CardDescription>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Update Balance
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Update Customer Balance</DialogTitle>
              <DialogDescription>
                Set the new balance amount for {customerName}. 
                Positive values indicate credit (overpayment), 
                negative values indicate due amount.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4 py-4">
                <div className="grid gap-2">
                  <Label htmlFor="amount">New Balance Amount (৳)</Label>
                  <Input
                    id="amount"
                    type="number"
                    step="0.01"
                    placeholder="Enter amount (e.g., -100 for due, 50 for credit)"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    required
                  />
                  <p className="text-xs text-muted-foreground">
                    Current balance: ৳{currentBalance}
                  </p>
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="reason">Reason for Update</Label>
                  <Textarea
                    id="reason"
                    placeholder="Enter reason for balance update..."
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    required
                    maxLength={255}
                  />
                </div>
              </div>
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "Updating..." : "Update Balance"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <BalanceIcon className="h-5 w-5" />
            <div>
              <div className="flex items-center space-x-2">
                <span className="text-2xl font-bold">
                  {balanceInfo.text}
                </span>
                <Badge variant={balanceInfo.variant}>
                  {currentBalance === 0 ? "Balanced" : currentBalance > 0 ? "Credit" : "Due"}
                </Badge>
              </div>
              <p className="text-sm text-muted-foreground">
                {balanceInfo.description}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
